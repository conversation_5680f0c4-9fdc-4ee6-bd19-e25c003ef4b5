#!/usr/bin/env python3
"""
测试新版视频模型的脚本
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:7799"
TEST_IMAGE_URL = "https://wan-ai-wan-2-2-5b.ms.show/gradio_api/file=/tmp/gradio/5c0c33488b2e5d085d8aa36fb9264b7189b19e1521f665b0fa19f8b58c0e4236/00061-3257823295.png"
TEST_PROMPT = "女孩的头发随风飘动"
TEST_RESOLUTION = "1280*704"

def test_new_video_model():
    """测试新版视频模型"""
    print("开始测试新版视频模型...")
    
    # 准备测试数据
    data = {
        'image_source': TEST_IMAGE_URL,
        'prompt': TEST_PROMPT,
        'resolution': TEST_RESOLUTION,
        'model_index': 12  # 新版视频模型的索引
    }
    
    print(f"测试参数:")
    print(f"- 图片URL: {TEST_IMAGE_URL}")
    print(f"- 提示词: {TEST_PROMPT}")
    print(f"- 分辨率: {TEST_RESOLUTION}")
    print(f"- 模型索引: 12")
    
    try:
        # 发送请求
        print("\n发送视频生成请求...")
        response = requests.post(f"{BASE_URL}/generate_video", data=data, timeout=300)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print("✅ 新版视频模型测试成功！")
                if 'video_url' in result.get('data', {}):
                    print(f"生成的视频URL: {result['data']['video_url']}")
                else:
                    print("⚠️ 响应中没有找到video_url")
            else:
                print(f"❌ 视频生成失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保应用程序正在运行")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_direct_api():
    """直接测试新版视频模型的API"""
    print("\n开始直接测试新版视频模型API...")
    
    api_url = "https://menyuma-wan2-25b.ms.show/gradio_api/call/generate_video"
    
    # 构建请求数据
    data = {
        "data": [
            {
                "path": TEST_IMAGE_URL,
                "meta": {
                    "_type": "gradio.FileData"
                }
            },
            TEST_PROMPT,
            TEST_RESOLUTION,
            4,
            10,
            5,
            5,
            -1
        ]
    }
    
    print(f"API URL: {api_url}")
    print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        # 发送请求
        print("\n发送API请求...")
        response = requests.post(api_url, headers={"Content-Type": "application/json"}, 
                               data=json.dumps(data), timeout=60)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            event_id = result.get("event_id")
            if event_id:
                print(f"✅ 获取到事件ID: {event_id}")
                print("可以使用此事件ID查询生成结果")
            else:
                print("❌ 未获取到事件ID")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ API测试过程中发生错误: {e}")

if __name__ == "__main__":
    print("=== 新版视频模型测试 ===")
    
    # 首先测试直接API调用
    test_direct_api()
    
    print("\n" + "="*50)
    
    # 然后测试通过我们的应用程序
    test_new_video_model()
    
    print("\n测试完成！")
